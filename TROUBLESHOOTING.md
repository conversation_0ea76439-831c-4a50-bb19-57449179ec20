# 浦江论坛项目 - 404问题解决方案

## 问题描述
访问项目时出现404错误，无法正常访问网站。

## 快速解决方案

### 1. 使用PHP内置服务器（推荐）
```bash
# 在项目根目录执行
php -S localhost:8000 -t public
```

然后访问：
- http://localhost:8000/ - 主页面
- http://localhost:8000/simple.php - 详细测试页面
- http://localhost:8000/test.php - 简单测试页面

### 2. 使用批处理文件（Windows）
双击运行 `start_server.bat` 文件

## 详细解决步骤

### 1. 数据库设置
1. 启动MySQL服务
2. 执行数据库初始化脚本：
   ```sql
   mysql -u root -p < init_database.sql
   ```
   或者手动创建数据库：
   ```sql
   CREATE DATABASE pujiangforum;
   ```

### 2. 检查PHP环境
访问 `http://localhost:8000/test.php` 检查：
- PHP版本 >= 7.0
- 数据库连接是否正常
- 必要扩展是否安装

### 3. 检查项目文件
访问 `http://localhost:8000/simple.php` 检查：
- 配置文件是否存在
- ThinkPHP框架文件是否完整
- 运行时目录权限是否正确

## 可能的原因和解决方案

### 1. 数据库连接问题
**问题**: 项目配置的数据库是阿里云RDS，本地开发环境无法连接。

**解决方案**:
- ✅ 已修改 `application/database.php` 文件，将数据库配置改为本地环境
- 确保本地MySQL服务已启动
- 创建名为 `pujiangforum` 的数据库
- 根据实际情况修改数据库用户名和密码

### 2. URL重写配置问题
**问题**: ThinkPHP需要URL重写支持，否则无法正确处理路由。

**解决方案**:
- ✅ 已创建 `public/.htaccess` 文件配置URL重写规则
- 确保Apache的mod_rewrite模块已启用
- 如果使用Nginx，需要配置相应的rewrite规则
- **推荐使用PHP内置服务器，无需配置URL重写**

### 3. 路由配置问题
**问题**: 路由规则可能不正确，导致无法匹配到正确的控制器。

**解决方案**:
- ✅ 已优化 `application/route.php` 文件
- 添加了本地开发环境的路由规则
- 支持localhost和127.0.0.1访问

### 4. Web服务器配置问题
**问题**: Web服务器配置不正确，无法正确处理PHP文件。

**解决方案**:
- 确保Web服务器（Apache/Nginx）已正确安装和配置
- 确保PHP已安装并启用
- 确保项目根目录指向 `public` 文件夹
- **推荐使用PHP内置服务器，配置简单**

## 测试步骤

### 1. 基础测试
访问: `http://localhost:8000/test.php`
如果能看到PHP信息，说明PHP环境正常。

### 2. 详细测试
访问: `http://localhost:8000/simple.php`
检查所有组件是否正常工作。

### 3. 数据库测试
在simple.php页面中会显示数据库连接状态。
如果连接失败，请检查：
- MySQL服务是否启动
- 数据库是否存在
- 用户名密码是否正确

### 4. 主页面测试
访问: `http://localhost:8000/`
应该能看到浦江论坛的首页。

### 5. 管理后台测试
访问: `http://localhost:8000/admin`
应该能看到管理后台登录页面。

## 常见错误及解决方法

### 错误1: "No input file specified"
**原因**: PHP配置问题或文件权限问题
**解决**: 
- 检查PHP配置
- 确保文件有正确的读取权限
- 使用PHP内置服务器

### 错误2: "Database connection failed"
**原因**: 数据库连接失败
**解决**:
- 检查MySQL服务状态
- 验证数据库配置信息
- 确保数据库已创建
- 执行 `init_database.sql` 脚本

### 错误3: "Page not found"
**原因**: 路由配置问题
**解决**:
- 使用PHP内置服务器
- 检查路由规则是否正确
- 访问测试页面确认环境

### 错误4: "ThinkPHP framework error"
**原因**: 框架文件缺失或配置错误
**解决**:
- 检查ThinkPHP文件是否完整
- 确保运行时目录可写
- 查看详细错误信息

## 开发环境要求

- PHP >= 7.0
- MySQL >= 5.6
- Apache/Nginx（可选，推荐使用PHP内置服务器）

## 快速启动

1. 启动MySQL服务
2. 执行数据库初始化：`mysql -u root -p < init_database.sql`
3. 启动PHP服务器：`php -S localhost:8000 -t public`
4. 访问 `http://localhost:8000/`

## 文件说明

- `public/index.php` - 主入口文件
- `public/simple.php` - 详细测试页面
- `public/test.php` - 简单测试页面
- `start_server.bat` - Windows启动脚本
- `init_database.sql` - 数据库初始化脚本
- `TROUBLESHOOTING.md` - 本故障排除文档

## 注意事项

- 项目使用了ThinkPHP 5.0框架
- 默认模块是 `web`
- 默认控制器是 `Index`
- 默认操作是 `index`
- 项目支持多语言（中文和英文）
- 包含移动端适配（m模块）
- **推荐使用PHP内置服务器进行开发** 