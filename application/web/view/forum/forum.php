<head>
    <link rel="stylesheet" href="<?=$dir_public?>/web/css/forum.css">
</head>
<div id="app" class="forum-container">
    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/web/images/pageBg.jpg) center center no-repeat;">
        <div class="box">
            <div class="wrap between">
                <div class="location left">
                    <div class="iconfont icon-shouye sel"></div>
                    <p>当前位置：</p>
                    <a href="/">首页</a>
                    <span class="iconfont icon-jiantou"></span>
                    <a href="javascript:;" class="on">论坛日程</a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container" style="max-width: 1400px; margin: 0 auto 87px;">
        <!-- 头部导航 -->
        <div class="forum-header">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div class="date-tabs">
                    <div class="date-tab" :class="sele === 1 ? 'active' : ''" @click="changeTime('2025-07-25', 1)">
                        7月25日
                    </div>
                    <div class="date-tab" :class="sele === 2 ? 'active' : ''" @click="changeTime('2025-07-26', 2)">
                        7月26日
                    </div>
                    <div class="date-tab" :class="sele === 3 ? 'active' : ''" @click="changeTime('2025-07-27', 3)">
                        7月27日
                    </div>
                </div>
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="搜索议程或嘉宾" v-model="searchKeyword">
                    <button class="search-btn"><img class="icon-search" src="<?=$dir_public?>/web/images/search.png" alt="时间"></button>
                </div>
            </div>
        </div>

        <!-- 日程列表 -->
        <div class="schedule-list">
            <div v-for="(item, index) in filteredLists" :key="index" class="schedule-item">
                <div class="schedule-header" :class="sel === index ? 'expanded' : ''" @click="select(index)">
                    <div class="schedule-time">
                        <span class="time-wrap">
                            <span v-if="sel === index"><img class="icon-clock" src="<?=$dir_public?>/web/images/clock_white.png" alt="时间"></span>
                            <span v-else><img class="icon-clock" src="<?=$dir_public?>/web/images/clock.png" alt="时间"></span>
                            <span class="time-text">{{item.start_time}} - {{item.end_time}}</span>
                        </span>
                        <span class="map-wrap">
                            <img v-if="sel === index" class="icon-map" src="<?=$dir_public?>/web/images/map_white.png" alt="地点">
                            <img v-else class="icon-map" src="<?=$dir_public?>/web/images/map.png" alt="地点">
                            <span class="map-text">会议室：{{item.room}}</span>
                        </span>
                    </div>
                    <div class="schedule-title">{{item.title}}</div>
                    <div class="expand-icon">
                        <span v-if="sel === index"><img class="icon-expand" src="<?=$dir_public?>/web/images/expand.png" alt="收起"></span>
                        <span v-else><img class="icon-expand" src="<?=$dir_public?>/web/images/retract.png" alt="展开"></span>
                    </div>
                </div>
                <div class="schedule-content" :class="sel === index ? 'expanded' : ''">
                    <div class="schedule-details">
                       <div class="details-wrap">
                        <!-- 论坛地点 -->
                        <div class="location">论坛地点：{{item.location}}</div>
                        
                        <!-- 主办方信息 -->
                        <div class="organizers" v-if="item.organizers">
                            <div class="organizer-row" v-if="item.organizers.host">
                                <div class="organizer-label">主办方：</div>
                                <div class="organizer-list">{{item.organizers.host.join('、')}}</div>
                            </div>
                            <div class="organizer-row" v-if="item.organizers.coHost">
                                <div class="organizer-label">协办方：</div>
                                <div class="organizer-list">{{item.organizers.coHost.join('、')}}</div>
                            </div>
                            <div class="organizer-row" v-if="item.organizers.support">
                                <div class="organizer-label">支持方：</div>
                                <div class="organizer-list">{{item.organizers.support.join('、')}}</div>
                            </div>
                        </div>

                        <!-- 主题描述 -->
                        <div class="description" v-if="item.description">
                            <div class="description-title">主题诠释</div>
                            <div class="description-content">{{item.description}}</div>
                        </div>
                       </div>

                        <!-- 时间轴议程 -->
                        <div class="timeline" v-if="item.timeline && item.timeline.length">
                            <div v-for="(timeItem, timeIndex) in item.timeline" :key="timeIndex" class="timeline-item">
                                <div class="timeline-time">{{timeItem.time}}</div>
                                <div class="timeline-dot-out">
                                    <div class="timeline-dot"></div>
                                    <!-- 只在不是最后一个时显示虚线 -->
                                    <div v-if="timeIndex !== item.timeline.length - 1" class="timeline-line"></div>
                                </div>
                                <div class="timeline-content">
                                    <div class="timeline-title" v-if="timeItem.title">{{timeItem.title}}</div>
                                    <div class="timeline-activity" v-if="timeItem.activities && timeItem.activities.length">
                                        <div v-for="(activity, activityIndex) in timeItem.activities" :key="activityIndex" class="activity-item">
                                            <span class="activity-name">{{activity.key}}</span>
                                            <span class="activity-info">{{activity.info}}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 使用嘉宾墙组件 -->
                        <div 
                            class="guests-details"
                            v-if="item.guests && item.guests.length">
                                <div class="guests-title">嘉宾墙</div>
                                <div class="guests-wrap">
                                    <div class="guests-grid">
                                        <div v-for="(guest, index) in item.guests" :key="index" 
                                            class="guest-item" 
                                            :class="{ 'current-speaker': isCurrentSpeaker(guest.name, item) }">
                                            <div class="guest-avatar">
                                                <img :src="guest.avatar" :alt="guest.name">
                                            </div>
                                            <div class="guest-name">{{guest.name}}</div>
                                            <div class="guest-title">{{guest.title}}</div>
                                        </div>
                                    </div>
                                </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    new Vue({
        el: '#app',
        data: {
            sele: 1,
            sel: null,
            searchKeyword: '',
            start_time: '2025-07-25 00:00:00',
            end_time: '2025-07-25 23:59:59',
            currentDate: '2025-07-25',
            lists: [
                {
                    id: 1,
                    date: '2025-07-25',
                    start_time: '09:30',
                    end_time: '21:00',
                    title: '开幕式暨主旨演讲（Y HUB）韩国科学技术院',
                    room: '主会场',
                    location: '中国·上海',
                    organizers: {
                        host: ['中华人民共和国科学技术部', '上海市人民政府'],
                        coHost: ['科技部国家科学技术委员会', '同济大学'],
                        support: ['科技部国家科学技术委员会', '同济大学']
                    },
                    description: '第十一次科技创新智库国际研讨会将在上海举办，将聚焦"科技创新中心建设"，区域科技创新中心建设，目标任务、上海、粤港澳大湾区三个国际创新中心建设正在加速推进。各地区创新资源配置的新格局，为大科技创新体系人才产业的力量，加强各方合作共赢的科技合作与交流，不断提升自主创新',
                    timeline: [
                        {
                            time: '09:00-09:30',
                            title: '开幕致辞',
                            activities: [
                                {
                                    key: '演讲嘉宾',
                                    info: '刘冬梅，中国科学技术发展战略研究院党委书记'
                                }
                            ]
                        },
                        {
                            time: '09:30-10:30',
                            title: '主旨演讲：应对颠覆性创新的公共治理策略',
                            activities: [
                                {
                                    key: '演讲嘉宾',
                                    info: '张大力，中国科学院院士'
                                }
                            ]
                        },
                        {
                            time: '10:30-11:30',
                            title: '新科技革命与体制机制创新',
                            activities: [
                                {
                                    key: '演讲嘉宾',
                                    info: '李明华，中国工程院院士'
                                }
                            ]
                        },
                        {
                            time: '14:00-15:00',
                            title: '人工智能发展现状与未来趋势',
                            activities: [
                                {
                                    key: '演讲嘉宾',
                                    info: '王晓燕，复旦大学教授'
                                }
                            ]
                        },
                        {
                            time: '15:30-16:30',
                            title: '5G技术与产业发展',
                            activities: [
                                {
                                    key: '演讲嘉宾',
                                    info: '刘建国，华为技术有限公司首席科学家'
                                }
                            ]
                        },
                        {
                            time: '17:00-18:00',
                            title: '云计算与数字化转型',
                            activities: [
                                {
                                    key: '演讲嘉宾',
                                    info: '赵雅琴，阿里巴巴集团首席技术官'
                                }
                            ]
                        },
                        {
                            time: '18:00-19:00',
                            title: '深度学习技术前沿',
                            speaker: '孙志强',
                            activities: [
                                {
                                    key: '演讲嘉宾',
                                    info: '孙志强，百度公司首席架构师'
                                }
                            ]
                        },
                        {
                            time: '19:00-20:00',
                            title: '推荐算法与用户体验',
                            activities: [
                                {
                                    key: '演讲嘉宾',
                                    info: '周美玲，字节跳动技术副总裁'
                                }
                            ]
                        },
                        {
                            time: '20:00-21:00',
                            title: '物联网技术发展趋势',
                            activities: [
                                {
                                    key: '演讲嘉宾',
                                    info: '吴天明，小米科技首席科学家'
                                }
                            ]
                        }
                    ],
                    guests: [
                        {
                            name: '郭大力',
                            title: '无名集团有限公司总经理创始人兼总人',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '郭一一',
                            title: '无名集团有限公司总经理创始人兼总人',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '全智贤',
                            title: '无名集团有限公司总经理创始人兼总人',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '赵雅琴',
                            title: '无名集团有限公司总经理创始人兼总人',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '徐智慧',
                            title: '无名集团有限公司总经理创始人兼总人',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '测试大',
                            title: '无名集团有限公司总经理创始人兼总人',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '郭一一',
                            title: '无名集团有限公司总经理创始人兼总人',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '全智贤',
                            title: '无名集团有限公司总经理创始人兼总人',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '邹雨青',
                            title: '无名集团有限公司总经理创始人兼总人',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '徐智慧',
                            title: '无名集团有限公司总经理创始人兼总人',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        }
                    ]
                },
                {
                    id: 2,
                    date: '2025-07-25',
                    start_time: '10:00',
                    end_time: '11:30',
                    title: '2024浦江创新论坛主论坛《新质生产力科技创新》"科学家+企业家"亚太未来论坛',
                    room: '主会场',
                    location: '中国·上海',
                    organizers: {
                        host: ['上海市科学技术委员会'],
                        coHost: ['浦东新区科技和经济委员会']
                    },
                    description: '聚焦新质生产力发展，探讨科技创新与产业转型升级的深度融合。',
                    timeline: [
                        {
                            time: '10:00-10:30',
                            title: '新质生产力的科技内涵与发展路径',
                            activities: [
                                {
                                    key: '演讲嘉宾',
                                    info: '周美玲，字节跳动技术副总裁'
                                }
                            ]
                        },
                        {
                            time: '10:30-10:45',
                            activities: [
                                    {
                                        key: '演讲嘉宾',
                                        info: '周美玲，字节跳动技术副总裁'
                                    }
                                ]
                        },
                        {
                            time: '10:45-11:00',
                            activities: [
                                {
                                    key: '演讲嘉宾',
                                    info: '周美玲，字节跳动技术副总裁'
                                }
                            ]
                        },
                        {
                            time: '11:00-11:30',
                            title: '圆桌讨论',
                            activities: [
                                {
                                    key: '演讲嘉宾',
                                    info: '周美玲，字节跳动技术副总裁'
                                }
                            ]
                        }
                    ],
                    guests: [
                        {
                            name: '张大力',
                            title: '中国科学院院士\n清华大学教授\n国际人工智能学会会士',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '李明华',
                            title: '中国工程院院士\n上海交通大学教授\n国际电气电子工程师学会会士',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '王晓燕',
                            title: '复旦大学教授\n生物医学工程专家\n国家杰出青年基金获得者',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '刘建国',
                            title: '华为技术有限公司\n首席科学家\n5G技术专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '陈思远',
                            title: '腾讯公司副总裁\n人工智能实验室主任\n机器学习专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '赵雅琴',
                            title: '阿里巴巴集团\n首席技术官\n云计算专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '孙志强',
                            title: '百度公司\n首席架构师\n深度学习专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '周美玲',
                            title: '字节跳动\n技术副总裁\n推荐算法专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '吴天明',
                            title: '小米科技\n首席科学家\n物联网专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '郑晓东',
                            title: '京东集团\n技术委员会主席\n大数据专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        }
                    ]
                },
                {
                    id: 3,
                    date: '2025-07-25',
                    start_time: '11:30',
                    end_time: '12:00',
                    title: '上海国际科技创新中心建设',
                    room: '分会场A',
                    location: '中国·上海',
                    organizers: {
                        host: ['上海市科学技术委员会']
                    },
                    description: '展示上海科创中心建设成果，分享创新发展经验。',
                    timeline: [
                        {
                            time: '11:30-11:45',
                            title: '开场致辞',
                            speaker: '王七',
                            speakerInfo: '上海市科学技术委员会主任'
                        },
                        {
                            time: '11:45-12:00',
                            title: '主题演讲',
                            speaker: '赵八',
                            speakerInfo: '上海交通大学教授'
                        }
                    ],
                    guests: [
                        {
                            name: '张大力',
                            title: '中国科学院院士\n清华大学教授\n国际人工智能学会会士',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '李明华',
                            title: '中国工程院院士\n上海交通大学教授\n国际电气电子工程师学会会士',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '王晓燕',
                            title: '复旦大学教授\n生物医学工程专家\n国家杰出青年基金获得者',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '刘建国',
                            title: '华为技术有限公司\n首席科学家\n5G技术专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '陈思远',
                            title: '腾讯公司副总裁\n人工智能实验室主任\n机器学习专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '赵雅琴',
                            title: '阿里巴巴集团\n首席技术官\n云计算专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '孙志强',
                            title: '百度公司\n首席架构师\n深度学习专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '周美玲',
                            title: '字节跳动\n技术副总裁\n推荐算法专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '吴天明',
                            title: '小米科技\n首席科学家\n物联网专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '郑晓东',
                            title: '京东集团\n技术委员会主席\n大数据专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        }
                    ]
                },
                {
                    id: 4,
                    date: '2025-07-25',
                    start_time: '18:00',
                    end_time: '20:00',
                    title: '前沿科技发展论坛',
                    room: '主会场',
                    location: '中国·上海',
                    organizers: {
                        host: ['中科院上海分院'],
                        support: ['上海交通大学', '复旦大学']
                    },
                    description: '探讨人工智能、量子计算、生物技术等前沿科技发展趋势。',
                    timeline: [
                        {
                            time: '18:00-19:00',
                            title: '人工智能发展现状与未来',
                            activities: [
                                {
                                    key: '演讲嘉宾',
                                    info: '张大力，字节跳动技术副总裁'
                                },
                                {
                                    key: '',
                                    info: '周美玲，字节跳动技术副总裁'
                                },
                                {
                                    key: '演讲嘉宾',
                                    info: '周美玲2，字节跳动技术副总裁'
                                }
                            ]
                        },
                        {
                            time: '19:00-20:00',
                            title: '互动讨论',
                            activities: [
                                {
                                    key: '演讲嘉宾',
                                    info: '周美玲3，字节跳动技术副总裁'
                                },
                                {
                                    key: '演讲嘉宾',
                                    info: '周美玲4，字节跳动技术副总裁'
                                },
                                {
                                    key: '演讲嘉宾',
                                    info: '周美玲5，字节跳动技术副总裁'
                                }
                            ]
                        }
                    ],
                    guests: [
                        {
                            name: '张大力',
                            title: '中国科学院院士\n清华大学教授\n国际人工智能学会会士',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '李明华',
                            title: '中国工程院院士\n上海交通大学教授\n国际电气电子工程师学会会士',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '王晓燕',
                            title: '复旦大学教授\n生物医学工程专家\n国家杰出青年基金获得者',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '刘建国',
                            title: '华为技术有限公司\n首席科学家\n5G技术专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '陈思远',
                            title: '腾讯公司副总裁\n人工智能实验室主任\n机器学习专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '赵雅琴',
                            title: '阿里巴巴集团\n首席技术官\n云计算专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '孙志强',
                            title: '百度公司\n首席架构师\n深度学习专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '周美玲',
                            title: '字节跳动\n技术副总裁\n推荐算法专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '吴天明',
                            title: '小米科技\n首席科学家\n物联网专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        },
                        {
                            name: '郑晓东',
                            title: '京东集团\n技术委员会主席\n大数据专家',
                            avatar: '<?=$dir_public?>/web/images/avatar.png'
                        }
                    ]
                }
            ]
        },
        computed: {
            filteredLists() {
                let filtered = this.lists.filter(item => item.date === this.currentDate);
                if (this.searchKeyword) {
                    filtered = filtered.filter(item => 
                        item.title.includes(this.searchKeyword) ||
                        item.room.includes(this.searchKeyword)
                    );
                }
                return filtered;
            }
        },
        created() {
            // 根据当前时间自动选择日期
            this.forumList(this.start_time, this.end_time)

            let nowTime = new Date().getTime()
            let startDate = new Date('2025-07-25 00:00:00').getTime()
            let endDate = new Date('2025-07-27 23:59:59').getTime()

            // 如果活动还没开始，默认显示第一天
            if(nowTime < startDate) {
                this.start_time = '2025-07-25 00:00:00'
                this.end_time = '2025-07-25 23:59:59'
                this.currentDate = '2025-07-25'
                this.sele = 1
            }
            // 如果活动已经结束，显示最后一天
            else if(nowTime > endDate) {
                this.start_time = '2025-07-27 00:00:00'
                this.end_time = '2025-07-27 23:59:59'
                this.currentDate = '2025-07-27'
                this.sele = 3
            }
            // 活动进行中，根据当前日期选择对应天数
            else {
                let day1End = new Date('2025-07-25 23:59:59').getTime()
                let day2End = new Date('2025-07-26 23:59:59').getTime()
                
                if(nowTime <= day1End) {
                    this.start_time = '2025-07-25 00:00:00'
                    this.end_time = '2025-07-25 23:59:59'
                    this.currentDate = '2025-07-25'
                    this.sele = 1
                }
                else if(nowTime <= day2End) {
                    this.start_time = '2025-07-26 00:00:00'
                    this.end_time = '2025-07-26 23:59:59'
                    this.currentDate = '2025-07-26'
                    this.sele = 2
                }
                else {
                    this.start_time = '2025-07-27 00:00:00'
                    this.end_time = '2025-07-27 23:59:59'
                    this.currentDate = '2025-07-27'
                    this.sele = 3
                }
            }

            this.forumList(this.start_time, this.end_time)
        },
        methods: {
            changeTime(date, index) {
                this.currentDate = date;
                this.sele = index;
                this.sel = null; // 重置展开状态

                // 根据选择的日期更新时间范围
                this.start_time = date + ' 00:00:00';
                this.end_time = date + ' 23:59:59';

                this.forumList(this.start_time, this.end_time)
            },
            select(index) {
                if (this.sel === index) {
                    // 收起当前项
                    const scheduleItems = document.querySelectorAll('.schedule-content');
                    if (scheduleItems && index < scheduleItems.length) {
                        const targetContent = scheduleItems[index];
                        targetContent.style.maxHeight = '0px';
                        targetContent.style.opacity = '0';
                        targetContent.style.transform = 'translateY(-10px)';
                    }
                    this.sel = null;
                } else {
                    this.sel = index; // 展开当前项

                    // 自适应高度处理
                    this.$nextTick(() => {
                        const scheduleItems = document.querySelectorAll('.schedule-content');
                        if (scheduleItems && index < scheduleItems.length) {
                            const targetContent = scheduleItems[index];
                            
                            // 先设置一个临时高度来获取实际内容高度
                            targetContent.style.maxHeight = 'none';
                            targetContent.style.opacity = '1';
                            targetContent.style.transform = 'translateY(0)';
                            
                            // 获取实际内容高度
                            const actualHeight = targetContent.scrollHeight;
                            
                            // 重置为收起状态
                            targetContent.style.maxHeight = '0px';
                            targetContent.style.opacity = '0';
                            targetContent.style.transform = 'translateY(-10px)';
                            
                            // 强制重绘
                            targetContent.offsetHeight;
                            
                            // 设置实际高度并展开
                            targetContent.style.maxHeight = actualHeight + 'px';
                            targetContent.style.opacity = '1';
                            targetContent.style.transform = 'translateY(0)';
                            
                            // 动画结束后移除固定高度，允许内容自适应
                            setTimeout(() => {
                                targetContent.style.maxHeight = 'none';
                            }, 300);
                        }
                        
                        // 滚动到当前卡片头部
                        setTimeout(() => {
                            const scheduleItems = document.querySelectorAll('.schedule-item');
                            if (scheduleItems && index < scheduleItems.length && scheduleItems[index]) {
                                const targetElement = scheduleItems[index];
                                const rect = targetElement.getBoundingClientRect();
                                const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
                                
                                // 计算目标位置，考虑固定头部高度
                                const headerHeight = 120; // 考虑页面头部高度
                                const targetScrollTop = currentScrollTop + rect.top - headerHeight;
                                
                                // 平滑滚动到目标位置
                                window.scrollTo({
                                    top: Math.max(0, targetScrollTop), // 确保不会滚动到负值
                                    behavior: 'smooth'
                                });
                            }
                        }, 150); // 给一点延迟确保动画开始后再滚动
                    });
                }
            },
            // 获取日程接口
            forumList(start, end){
                var that = this
                axios({
                    method: 'post',
                    url: '<?=url('api/schedule/schedule_zs')?>',
                    data: {
                        start_time: start,
                        end_time: end,
                    }
                }).then((res) => {
                    if (res.data.code == 200) {
                        let lists = res.data.data
                        lists.forEach((item, index) => {
                            let now = new Date().getTime()
                            let startTime = new Date(item.lt.forumtime[0]).getTime()
                            let endTime = new Date(item.lt.forumtime[1]).getTime()
                            item.lt.start_time = item.lt.forumtime[0].slice(5, 16)
                            item.lt.end_time = item.lt.forumtime[1].slice(10, 16)
                            if(now < startTime) {
                                item.lt.forumtime_type = 1  // 未开始
                            }
                            if(now > startTime && now < endTime) {
                                item.lt.forumtime_type = 2  // 进行中
                            }
                            if(now > endTime) {
                                item.lt.forumtime_type = 3  // 已结束
                            }
                        })
                        this.lists = lists
                    }
                })
            },
            // 判断是否为当前演讲嘉宾
            isCurrentSpeaker(guestName, item) {
                const now = new Date();
                const currentTime = now.getHours() * 60 + now.getMinutes();
                
                // 获取当前选中的日期
                const selectedDate = this.currentDate;
                const currentDateStr = now.getFullYear() + '-' + 
                    String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                    String(now.getDate()).padStart(2, '0');
                
                // 检查日期是否匹配
                if (selectedDate !== currentDateStr) {
                    return false;
                }
                
                if (item.timeline) {
                    for (let timeItem of item.timeline) {
                        const [startHour, startMin] = timeItem.time.split('-')[0].split(':').map(Number);
                        const [endHour, endMin] = timeItem.time.split('-')[1].split(':').map(Number);
                        const startTime = startHour * 60 + startMin;
                        const endTime = endHour * 60 + endMin;
                        
                        if (currentTime >= startTime && currentTime <= endTime) {
                            // 检查 speaker 字段
                            if (timeItem.speaker && timeItem.speaker === guestName) {
                                return true;
                            }
                            
                            // 检查 activities 数组
                            if (timeItem.activities) {
                                for (let activity of timeItem.activities) {
                                    if (activity.key === '演讲嘉宾' && activity.info.includes(guestName)) {
                                        return true;
                                    }
                                }
                            }
                        }
                    }
                }
                return false;
            }
        }
    });
</script>

