-- 浦江论坛数据库初始化脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `pujiangforum` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `pujiangforum`;

-- 创建基本表结构（示例）

-- Banner表
CREATE TABLE IF NOT EXISTS `banner` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `sort` int(11) DEFAULT 0,
  `language` tinyint(1) DEFAULT 1,
  `genre` tinyint(1) DEFAULT 1,
  `is_del` tinyint(1) DEFAULT 1,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 新闻表
CREATE TABLE IF NOT EXISTS `news` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `content` text,
  `sort_id` int(11) DEFAULT 0,
  `sort` int(11) DEFAULT 0,
  `language` tinyint(1) DEFAULT 1,
  `genre` tinyint(1) DEFAULT 1,
  `is_del` tinyint(1) DEFAULT 1,
  `release_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 合作伙伴分类表
CREATE TABLE IF NOT EXISTS `cooperate_sort` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `type` tinyint(1) DEFAULT 1,
  `sort` int(11) DEFAULT 0,
  `language` tinyint(1) DEFAULT 1,
  `genre` tinyint(1) DEFAULT 1,
  `is_del` tinyint(1) DEFAULT 1,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 合作伙伴表
CREATE TABLE IF NOT EXISTS `cooperate` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `sort_id` int(11) DEFAULT 0,
  `sort` int(11) DEFAULT 0,
  `language` tinyint(1) DEFAULT 1,
  `genre` tinyint(1) DEFAULT 1,
  `is_del` tinyint(1) DEFAULT 1,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 往届回顾报告表
CREATE TABLE IF NOT EXISTS `review_report` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `content` text,
  `review_id` int(11) DEFAULT 0,
  `sort` int(11) DEFAULT 0,
  `language` tinyint(1) DEFAULT 1,
  `genre` tinyint(1) DEFAULT 1,
  `is_del` tinyint(1) DEFAULT 1,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- TDK设置表
CREATE TABLE IF NOT EXISTS `rule_tdk` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `key` varchar(255) DEFAULT NULL,
  `desc` text,
  `language` tinyint(1) DEFAULT 1,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入一些测试数据
INSERT INTO `banner` (`title`, `image`, `url`, `sort`, `language`, `genre`, `is_del`) VALUES
('浦江论坛首页Banner', '/public/images/banner1.jpg', '#', 1, 1, 1, 1);

INSERT INTO `news` (`title`, `content`, `sort_id`, `sort`, `language`, `genre`, `is_del`) VALUES
('浦江论坛2024年会议通知', '浦江论坛2024年会议即将召开...', 1, 1, 1, 1, 1),
('浦江发布：最新政策解读', '最新政策解读内容...', 4, 1, 1, 1, 1);

INSERT INTO `cooperate_sort` (`name`, `type`, `sort`, `language`, `genre`, `is_del`) VALUES
('主办单位', 1, 1, 1, 1, 1),
('承办单位', 1, 2, 1, 1, 1),
('合作媒体', 3, 3, 1, 1, 1);

INSERT INTO `rule_tdk` (`title`, `key`, `desc`, `language`) VALUES
('浦江论坛 - 首页', '浦江论坛,论坛,会议', '浦江论坛官方网站', 1);

-- 显示创建结果
SELECT '数据库初始化完成！' as message; 