@echo off
echo 浦江论坛 - 404问题快速修复脚本
echo ======================================
echo.

echo 1. 检查PHP环境...
php -v
if %errorlevel% neq 0 (
    echo 错误：PHP未安装或未添加到PATH
    pause
    exit /b 1
)
echo.

echo 2. 创建必要的运行时目录...
if not exist runtime\cache mkdir runtime\cache
if not exist runtime\temp mkdir runtime\temp
if not exist runtime\log mkdir runtime\log
echo ✓ 运行时目录创建完成
echo.

echo 3. 检查数据库连接...
php -r "try { new PDO('mysql:host=localhost;dbname=pujiangforum;charset=utf8mb4', 'root', ''); echo '数据库连接成功\n'; } catch (Exception \$e) { echo '数据库连接失败: ' . \$e->getMessage() . '\n'; }"
echo.

echo 4. 启动PHP开发服务器...
echo 服务器地址: http://localhost:8000
echo 测试页面: http://localhost:8000/minimal.php
echo 调试页面: http://localhost:8000/debug.php
echo.
echo 按 Ctrl+C 停止服务器
echo.

php -S localhost:8000 -t public
pause 